import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { auth } from "@/auth";
import { withAdminRateLimit } from "@/lib/rate-limit-middleware";
import { RATE_LIMIT_TIERS } from "@/lib/rate-limit";

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * Toggle featured status for a hub
 * PATCH /api/admin/hubs/[id]/featured
 */
async function handlePATCH(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication and admin status
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user is admin (you may need to adjust this based on your admin system)
    // For now, checking if user has admin role or is a specific admin user
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { id: true, username: true }
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    // TODO: Add proper admin role checking here
    // For now, this is a placeholder - you should implement proper admin checking
    // const isAdmin = user.role === 'ADMIN' || user.isAdmin || ADMIN_USER_IDS.includes(user.id);
    // if (!isAdmin) {
    //   return NextResponse.json(
    //     { success: false, error: "Admin access required" },
    //     { status: 403 }
    //   );
    // }

    const hubId = params.id;
    
    // Parse request body
    const body = await request.json();
    const { featured } = body;

    if (typeof featured !== 'boolean') {
      return NextResponse.json(
        { success: false, error: "Featured status must be a boolean" },
        { status: 400 }
      );
    }

    // Check if hub exists
    const existingHub = await prisma.hub.findUnique({
      where: { id: hubId },
      select: { id: true, name: true, featured: true }
    });

    if (!existingHub) {
      return NextResponse.json(
        { success: false, error: "Hub not found" },
        { status: 404 }
      );
    }

    // Update the hub's featured status
    const updatedHub = await prisma.hub.update({
      where: { id: hubId },
      data: { featured },
      select: {
        id: true,
        name: true,
        featured: true,
        verified: true,
        partnered: true,
        iconUrl: true,
        bannerUrl: true,
        shortDescription: true,
        description: true,
        _count: {
          select: {
            connections: { where: { connected: true } }
          }
        }
      }
    });

    // Log the admin action (optional)
    console.log(`Admin ${user.username} (${user.id}) ${featured ? 'featured' : 'unfeatured'} hub ${updatedHub.name} (${hubId})`);

    return NextResponse.json({
      success: true,
      data: {
        hub: updatedHub,
        action: featured ? 'featured' : 'unfeatured',
        adminUser: user.username
      },
      message: `Hub ${featured ? 'featured' : 'unfeatured'} successfully`
    });

  } catch (error) {
    console.error("Error updating hub featured status:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Get current featured status for a hub
 * GET /api/admin/hubs/[id]/featured
 */
async function handleGET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const hubId = params.id;

    // Get hub's current featured status
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },
      select: {
        id: true,
        name: true,
        featured: true,
        verified: true,
        partnered: true
      }
    });

    if (!hub) {
      return NextResponse.json(
        { success: false, error: "Hub not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: hub
    });

  } catch (error) {
    console.error("Error fetching hub featured status:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Apply rate limiting
export const PATCH = withAdminRateLimit(handlePATCH, {
  tier: RATE_LIMIT_TIERS.STRICT,
});

export const GET = withAdminRateLimit(handleGET, {
  tier: RATE_LIMIT_TIERS.LENIENT,
});
