'use client';

import { UnifiedHubHeader, UnifiedHubHeaderSkeleton } from './unified-hub-header';
import { HubNavigationTabs, HubNavigationTabsSkeleton } from './hub-navigation-tabs';
import { Suspense } from 'react';

interface HubLayoutProps {
  hub: {
    id: string;
    name: string;
    description: string;
    iconUrl: string;
    private: boolean;
    nsfw: boolean;
    connectionCount: number;
  };
  currentTab: string;
  canModerate?: boolean;
  canEdit?: boolean;
  showBackButton?: boolean;
  backHref?: string;
  headerActions?: React.ReactNode;
  children: React.ReactNode;
}

export function HubLayout({
  hub,
  currentTab,
  canModerate = false,
  canEdit = false,
  showBackButton = true,
  backHref = '/dashboard/hubs',
  headerActions,
  children,
}: HubLayoutProps) {
  return (
    <div className="space-y-8">
      {/* Unified Hub Header */}
      <UnifiedHubHeader
        hub={hub}
        showBackButton={showBackButton}
        backHref={backHref}
        actions={headerActions}
      />

      {/* Navigation Tabs */}
      <div className="sticky top-0 z-20 bg-gray-950/80 backdrop-blur-md border-b border-gray-800/50">
        <HubNavigationTabs
          hubId={hub.id}
          currentTab={currentTab}
          canModerate={canModerate}
          canEdit={canEdit}
        />
      </div>

      {/* Page Content */}
      <div className="space-y-6">{children}</div>
    </div>
  );
}

// Loading skeleton for the hub layout
export function HubLayoutSkeleton({
  currentTab = 'overview',
  children,
}: {
  currentTab?: string;
  children?: React.ReactNode;
}) {
  return (
    <div className="space-y-8">
      {/* Header Skeleton */}
      <UnifiedHubHeaderSkeleton />

      {/* Navigation Tabs Skeleton */}
      <div className="sticky top-0 z-20 bg-gray-950/80 backdrop-blur-md border-b border-gray-800/50">
        <HubNavigationTabsSkeleton currentTab={currentTab} />
      </div>

      {/* Content Skeleton */}
      <div className="space-y-6">
        {children || (
          <div className="space-y-4">
            <div className="h-64 bg-gray-800/50 rounded-xl animate-pulse" />
            <div className="h-48 bg-gray-800/50 rounded-xl animate-pulse" />
            <div className="h-32 bg-gray-800/50 rounded-xl animate-pulse" />
          </div>
        )}
      </div>
    </div>
  );
}

// Wrapper component for pages that need to fetch hub data
interface HubLayoutWrapperProps {
  hubId: string;
  currentTab: string;
  canModerate?: boolean;
  canEdit?: boolean;
  showBackButton?: boolean;
  backHref?: string;
  headerActions?: React.ReactNode;
  children: (hub: HubLayoutProps['hub']) => React.ReactNode;
  fallback?: React.ReactNode;
}

export function HubLayoutWrapper({ currentTab, fallback }: HubLayoutWrapperProps) {
  // This would typically fetch hub data, but since we're working with server components
  // in most cases, we'll keep this simple for now
  return (
    <Suspense fallback={fallback || <HubLayoutSkeleton currentTab={currentTab} />}>
      {/* This component would be used in client components that need to fetch hub data */}
      <div>Hub data fetching would go here</div>
    </Suspense>
  );
}
