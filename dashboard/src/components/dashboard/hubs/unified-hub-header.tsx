'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Users, Globe, Lock, Eye, EyeOff } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface UnifiedHubHeaderProps {
  hub: {
    id: string;
    name: string;
    description: string;
    iconUrl: string;
    private: boolean;
    nsfw: boolean;
    connectionCount: number;
  };
  showBackButton?: boolean;
  backHref?: string;
  actions?: React.ReactNode;
}

export function UnifiedHubHeader({
  hub,
  showBackButton = true,
  backHref = '/dashboard/hubs',
  actions,
}: UnifiedHubHeaderProps) {
  return (
    <div className="space-y-6">
      {/* Top Navigation Bar */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          {showBackButton && (
            <Button
              variant="ghost"
              size="sm"
              className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white transition-all duration-200"
              asChild
            >
              <Link href={backHref}>
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </Link>
            </Button>
          )}
        </div>
        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </div>

      {/* Hub Header Card */}
      <div className="bg-gradient-to-r from-gray-900/90 via-gray-900/80 to-gray-950/90 backdrop-blur-sm border border-gray-800/50 rounded-xl p-6 shadow-lg">
        <div className="flex flex-col lg:flex-row lg:items-center gap-6">
          {/* Hub Icon and Basic Info */}
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className="h-20 w-20 rounded-2xl border-2 border-gray-700/50 overflow-hidden flex-shrink-0 shadow-lg">
                <Image
                  src={hub.iconUrl}
                  alt={hub.name}
                  width={80}
                  height={80}
                  className="w-full h-full object-cover"
                />
              </div>
              {/* Privacy indicator */}
              <div className="absolute -bottom-1 -right-1 bg-gray-950 rounded-full p-1.5 border border-gray-700/50 shadow-md">
                {hub.private ? (
                  <Lock className="h-3 w-3 text-amber-400" />
                ) : (
                  <Globe className="h-3 w-3 text-green-400" />
                )}
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl lg:text-3xl font-bold text-white truncate">
                  {hub.name}
                </h1>
                <div className="flex items-center gap-2">
                  {hub.private && (
                    <Badge variant="secondary" className="bg-amber-500/20 text-amber-400 border-amber-500/30">
                      <Lock className="h-3 w-3 mr-1" />
                      Private
                    </Badge>
                  )}
                  {hub.nsfw && (
                    <Badge variant="secondary" className="bg-red-500/20 text-red-400 border-red-500/30">
                      <EyeOff className="h-3 w-3 mr-1" />
                      NSFW
                    </Badge>
                  )}
                </div>
              </div>

              <p className="text-gray-300 text-sm lg:text-base leading-relaxed mb-3">
                {hub.description}
              </p>

              {/* Connection Count */}
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-800/50 rounded-lg border border-gray-700/50">
                  <Users className="h-4 w-4 text-blue-400" />
                  <span className="text-sm font-medium text-gray-300">
                    {hub.connectionCount} {hub.connectionCount === 1 ? 'server' : 'servers'} connected
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Loading skeleton for the unified header
export function UnifiedHubHeaderSkeleton() {
  return (
    <div className="space-y-6">
      {/* Top Navigation Bar Skeleton */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          <div className="h-9 w-20 bg-gray-800/50 rounded-md animate-pulse" />
        </div>
      </div>

      {/* Hub Header Card Skeleton */}
      <div className="bg-gradient-to-r from-gray-900/90 via-gray-900/80 to-gray-950/90 backdrop-blur-sm border border-gray-800/50 rounded-xl p-6 shadow-lg">
        <div className="flex flex-col lg:flex-row lg:items-center gap-6">
          <div className="flex items-center gap-4">
            <div className="h-20 w-20 bg-gray-800/50 rounded-2xl animate-pulse" />
            <div className="flex-1 min-w-0 space-y-3">
              <div className="flex items-center gap-3">
                <div className="h-8 w-48 bg-gray-800/50 rounded animate-pulse" />
                <div className="h-6 w-16 bg-gray-800/50 rounded animate-pulse" />
              </div>
              <div className="space-y-2">
                <div className="h-4 w-full bg-gray-800/50 rounded animate-pulse" />
                <div className="h-4 w-3/4 bg-gray-800/50 rounded animate-pulse" />
              </div>
              <div className="h-8 w-32 bg-gray-800/50 rounded-lg animate-pulse" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
